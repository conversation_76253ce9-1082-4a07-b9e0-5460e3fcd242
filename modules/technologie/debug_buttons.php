<?php
/**
 * Debug skript pro z<PERSON>š<PERSON><PERSON><PERSON><PERSON>, pro<PERSON> jsou t<PERSON> disabled
 */

// <PERSON><PERSON><PERSON><PERSON>, že je skript spuštěn z PrestaShop prostředí
if (!defined('_PS_VERSION_')) {
    require_once dirname(__FILE__) . '/../../config/config.inc.php';
}

echo "<h2>Debug tlačítek technologií</h2>\n";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .tech-debug { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
    .has-slug { background-color: #d4edda; border-color: #c3e6cb; }
    .no-slug { background-color: #f8d7da; border-color: #f5c6cb; }
    .code { font-family: monospace; background: #f8f9fa; padding: 5px; border-radius: 3px; }
    .debug-info { background: #e9ecef; padding: 10px; border-radius: 5px; margin: 10px 0; }
</style>\n";

try {
    // Simulace načtení technologií stejně jako v controlleru
    $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'technologie`
            WHERE active = 1
            ORDER BY position ASC, name ASC';

    $results = Db::getInstance()->executeS($sql);

    if (!$results) {
        echo "<p style='color: red;'>❌ Žádné technologie nenalezeny v databázi</p>\n";
        exit;
    }

    echo "<p>Nalezeno <strong>" . count($results) . "</strong> aktivních technologií v databázi:</p>\n";

    // Simulace logiky z controlleru
    foreach ($results as $row) {
        $tech = new stdClass();
        $tech->id = (int)$row['id_technologie'];
        $tech->name = $row['name'];
        $tech->description = $row['description'];
        $tech->image = $row['image'];
        $tech->position = (int)$row['position'];
        $tech->active = (bool)$row['active'];

        // Nové vlastnosti pro detail
        $tech->slug = $row['slug'] ?? null;
        $tech->advantages = $row['advantages'] ?? null;
        $tech->applications = $row['applications'] ?? null;
        $tech->detailed_description = $row['detailed_description'] ?? null;

        // Simulace generování URL
        if ($tech->slug) {
            $baseUrl = Tools::getHttpHost(true);
            $customUrl = $baseUrl . '/reklamni-potisk/' . $tech->slug;
            $fallbackUrl = Context::getContext()->link->getModuleLink('technologie', 'technologie', ['slug' => $tech->slug]);
            $tech->detail_url = $customUrl ?: $fallbackUrl;
        } else {
            $tech->detail_url = null;
        }

        // Zobrazení debug informací
        $cssClass = $tech->slug ? 'has-slug' : 'no-slug';
        echo "<div class='tech-debug $cssClass'>\n";
        echo "<h3>" . htmlspecialchars($tech->name) . "</h3>\n";
        
        echo "<div class='debug-info'>\n";
        echo "<strong>Databázové hodnoty:</strong><br>\n";
        echo "ID: " . $tech->id . "<br>\n";
        echo "Slug: <span class='code'>" . ($tech->slug ?: '❌ NULL/prázdné') . "</span><br>\n";
        echo "Aktivní: " . ($tech->active ? '✅ Ano' : '❌ Ne') . "<br>\n";
        echo "Pozice: " . $tech->position . "<br>\n";
        echo "</div>\n";

        echo "<div class='debug-info'>\n";
        echo "<strong>Generování URL:</strong><br>\n";
        if ($tech->slug) {
            echo "✅ Slug existuje → URL bude vygenerována<br>\n";
            echo "Detail URL: <span class='code'>" . htmlspecialchars($tech->detail_url) . "</span><br>\n";
            echo "<strong>Výsledek v šabloně:</strong> <span style='color: green;'>Aktivní odkaz</span><br>\n";
        } else {
            echo "❌ Slug neexistuje → URL nebude vygenerována<br>\n";
            echo "Detail URL: <span class='code'>NULL</span><br>\n";
            echo "<strong>Výsledek v šabloně:</strong> <span style='color: red;'>Disabled tlačítko</span><br>\n";
        }
        echo "</div>\n";

        echo "<div class='debug-info'>\n";
        echo "<strong>Šablona logika:</strong><br>\n";
        echo "<code>\n";
        if ($tech->detail_url) {
            echo "{if \$tech->detail_url} → PRAVDA<br>\n";
            echo "&nbsp;&nbsp;&lt;a href=\"{$tech->detail_url}\"&gt;Detail technologie&lt;/a&gt;<br>\n";
        } else {
            echo "{if \$tech->detail_url} → NEPRAVDA<br>\n";
            echo "&nbsp;&nbsp;&lt;button disabled&gt;Více informací&lt;/button&gt;<br>\n";
        }
        echo "</code>\n";
        echo "</div>\n";

        if ($tech->detail_url) {
            echo "<p><a href='" . htmlspecialchars($tech->detail_url) . "' target='_blank'>🔗 Test odkaz</a></p>\n";
        }

        echo "</div>\n";
    }

    // Celkové statistiky
    $withSlug = array_filter($results, function($row) { return !empty($row['slug']); });
    $withoutSlug = count($results) - count($withSlug);

    echo "<h3>Celkové statistiky:</h3>\n";
    echo "<ul>\n";
    echo "<li>Technologie se slug (aktivní tlačítka): <strong style='color: green;'>" . count($withSlug) . "</strong></li>\n";
    echo "<li>Technologie bez slug (disabled tlačítka): <strong style='color: red;'>" . $withoutSlug . "</strong></li>\n";
    echo "</ul>\n";

    if ($withoutSlug > 0) {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
        echo "<h4 style='color: #856404; margin-top: 0;'>⚠️ Problém identifikován!</h4>\n";
        echo "<p>Některé technologie nemají nastavený slug v databázi, proto se tlačítka zobrazují jako disabled.</p>\n";
        echo "<p><strong>Řešení:</strong> Spusťte <a href='update_database.php'>update_database.php</a> pro přidání chybějících slug hodnot.</p>\n";
        echo "</div>\n";
    } else {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
        echo "<h4 style='color: #155724; margin-top: 0;'>✅ Vše v pořádku!</h4>\n";
        echo "<p>Všechny technologie mají správně nastavený slug. Tlačítka by měla být aktivní.</p>\n";
        echo "</div>\n";
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Chyba: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<hr>\n";
echo "<p><a href='update_database.php'>🔧 Spustit aktualizaci databáze</a></p>\n";
echo "<p><a href='test_urls.php'>🔗 Test URL generování</a></p>\n";
echo "<p><a href='/reklamni-potisk'>📄 Přejít na stránku technologií</a></p>\n";
?>
