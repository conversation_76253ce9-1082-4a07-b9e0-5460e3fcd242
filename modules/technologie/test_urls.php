<?php
/**
 * Testovací skript pro ověření generování URL technologií
 */

// Kontrola, že je skript spuštěn z PrestaShop prostředí
if (!defined('_PS_VERSION_')) {
    require_once dirname(__FILE__) . '/../../config/config.inc.php';
}

echo "<h2>Test generování URL pro technologie</h2>\n";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .tech-item { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
    .success { background-color: #d4edda; border-color: #c3e6cb; }
    .error { background-color: #f8d7da; border-color: #f5c6cb; }
    .url { font-family: monospace; background: #f8f9fa; padding: 5px; border-radius: 3px; }
</style>\n";

try {
    // Načtení technologií z databáze
    $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'technologie` WHERE active = 1 ORDER BY position ASC';
    $technologie = Db::getInstance()->executeS($sql);
    
    if (!$technologie) {
        echo "<p style='color: red;'>❌ Žádné technologie nenalezeny v databázi</p>\n";
        exit;
    }
    
    echo "<p>Nalezeno <strong>" . count($technologie) . "</strong> aktivních technologií:</p>\n";
    
    foreach ($technologie as $tech) {
        $hasSlug = !empty($tech['slug']);
        $cssClass = $hasSlug ? 'success' : 'error';
        
        echo "<div class='tech-item $cssClass'>\n";
        echo "<h3>" . htmlspecialchars($tech['name']) . "</h3>\n";
        echo "<p><strong>ID:</strong> " . $tech['id_technologie'] . "</p>\n";
        echo "<p><strong>Slug:</strong> " . ($tech['slug'] ?: '❌ CHYBÍ') . "</p>\n";
        echo "<p><strong>Aktivní:</strong> " . ($tech['active'] ? '✅ Ano' : '❌ Ne') . "</p>\n";
        
        if ($hasSlug) {
            // Generování URL
            $context = Context::getContext();
            
            // Custom URL
            $baseUrl = Tools::getHttpHost(true);
            $customUrl = $baseUrl . '/reklamni-potisk/' . $tech['slug'];
            
            // Fallback URL
            $fallbackUrl = $context->link->getModuleLink('technologie', 'technologie', ['slug' => $tech['slug']]);
            
            echo "<p><strong>Custom URL:</strong> <span class='url'>" . htmlspecialchars($customUrl) . "</span></p>\n";
            echo "<p><strong>Fallback URL:</strong> <span class='url'>" . htmlspecialchars($fallbackUrl) . "</span></p>\n";
            
            // Test odkazy
            echo "<p><strong>Test odkazy:</strong></p>\n";
            echo "<ul>\n";
            echo "<li><a href='" . htmlspecialchars($customUrl) . "' target='_blank'>🔗 Custom URL</a></li>\n";
            echo "<li><a href='" . htmlspecialchars($fallbackUrl) . "' target='_blank'>🔗 Fallback URL</a></li>\n";
            echo "</ul>\n";
        } else {
            echo "<p style='color: red;'>❌ Nelze generovat URL - chybí slug</p>\n";
        }
        
        echo "</div>\n";
    }
    
    // Celkové statistiky
    $withSlug = array_filter($technologie, function($tech) { return !empty($tech['slug']); });
    $withoutSlug = count($technologie) - count($withSlug);
    
    echo "<h3>Statistiky:</h3>\n";
    echo "<ul>\n";
    echo "<li>Technologie se slug: <strong>" . count($withSlug) . "</strong></li>\n";
    echo "<li>Technologie bez slug: <strong>" . $withoutSlug . "</strong></li>\n";
    echo "</ul>\n";
    
    if ($withoutSlug > 0) {
        echo "<p style='color: red;'>⚠️ Některé technologie nemají nastavený slug. Spusťte update_database.php pro opravu.</p>\n";
    } else {
        echo "<p style='color: green;'>✅ Všechny technologie mají správně nastavený slug!</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Chyba: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<hr>\n";
echo "<p><a href='update_database.php'>🔧 Spustit aktualizaci databáze</a></p>\n";
echo "<p><a href='/reklamni-potisk'>🔗 Přejít na stránku technologií</a></p>\n";
?>
