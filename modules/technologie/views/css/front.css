/**
 * CSS styly pro front office modul Technologie potisku
 * Kompatibilní s PrestaShop 8.2.0 a moderními <PERSON>
 * Modernizovaný design s lep<PERSON><PERSON> UX a přístupností
 */

/* Import FontAwesome pokud nen<PERSON> */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css');

/* CSS Custom Properties pro konzistentní design */
:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #64748b;
    --accent-color: #0ea5e9;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;

    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;

    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;

    --border-color: #e2e8f0;
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --border-radius-xl: 24px;

    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Fallback pro ikonky pokud FontAwesome není dostupný */
.fas::before, .fa::before {
    font-family: "Font Awesome 6 Free", "Font Awesome 5 Free", "FontAwesome", sans-serif;
    font-weight: 900;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
}

/* Fallback obsah pro specifické ikonky */
.fa-print::before { content: "\f02f"; }
.fa-info-circle::before { content: "\f05a"; }
.fa-envelope::before { content: "\f0e0"; }
.fa-map-marker-alt::before { content: "\f3c5"; }
.fa-home::before { content: "\f015"; }
.fa-image::before { content: "\f03e"; }

/* Základní styly pro stránku technologií */
.technologie-page {
    padding: 0;
    background: var(--bg-secondary);
    min-height: 60vh;
}



/* Úvodní sekce - jednoduchý text bez animací */
.technologie-intro {
    background: var(--bg-primary);
    color: var(--text-primary);
    padding: 2rem 0;
    margin-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.technologie-intro .container {
    padding-left: 20px;
    padding-right: 20px;
}

.technologie-intro .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    color: var(--text-primary);
    letter-spacing: -0.025em;
    line-height: 1.2;
}

.technologie-intro .section-description {
    font-size: 1.125rem;
    line-height: 1.6;
    max-width: 900px;
    margin: 0 auto;
    font-weight: 400;
    color: var(--text-secondary);
}

/* Grid technologií */
.technologie-grid {
    padding: 20px 0;
    background: var(--bg-secondary);
}

.technologie-grid .container {
    max-width: 1600px;
    padding-left: 20px;
    padding-right: 20px;
}

/* Zajištění 3 sloupců na širších obrazovkách */
@media (min-width: 1200px) {
    .technologie-grid .container {
        max-width: 100%;
        padding-left: 20px;
        padding-right: 20px;
    }
}

/* Pro extra velké monitory */
@media (min-width: 1600px) {
    .technologie-grid .container {
        max-width: 100%;
        padding-left: 20px;
        padding-right: 20px;
    }
}

/* Zajištění stejné výšky boxů a mezer mezi řádky */
.technologie-grid .row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -1rem;
}

.technologie-grid .row > [class*="col-"] {
    display: flex;
    padding: 0 1rem;
    margin-bottom: 2rem;
}

/* Karta technologie - modernizovaný design */
.technologie-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all var(--transition-normal);
    height: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid var(--border-color);
    position: relative;
}

.technologie-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(37, 99, 235, 0.02) 100%);
    opacity: 0;
    transition: opacity var(--transition-normal);
    pointer-events: none;
}

.technologie-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.technologie-card:hover::before {
    opacity: 1;
}

/* Obrázek technologie */
.technologie-image {
    position: relative;
    height: 280px;
    overflow: hidden;
    background: var(--bg-tertiary);
}

.technologie-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
    filter: brightness(1.02) contrast(1.05);
}

.technologie-card:hover .technologie-img {
    transform: scale(1.08);
}

/* Placeholder pro obrázek */
.technologie-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
    color: var(--text-muted);
    position: relative;
}

.technologie-placeholder::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 80px;
    height: 80px;
    background: var(--primary-color);
    opacity: 0.1;
    border-radius: 50%;
    transform: translate(-50%, -50%);
}

.technologie-placeholder i {
    font-size: 3.5rem;
    margin-bottom: 0.75rem;
    color: var(--primary-color);
    opacity: 0.6;
    position: relative;
    z-index: 1;
}

.technologie-placeholder span {
    font-size: 0.875rem;
    font-weight: 500;
    position: relative;
    z-index: 1;
}



/* Obsah karty */
.technologie-content {
    padding: 2rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    background: var(--bg-primary);
}

.technologie-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
    line-height: 1.3;
    letter-spacing: -0.025em;
}

.technologie-description {
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 1.5rem;
    flex-grow: 1;
    font-size: 0.95rem;
}

/* Akce v kartě */
.technologie-actions {
    margin-top: auto;
    padding-top: 1rem;
}

.technologie-detail-btn {
    background: var(--bg-secondary);
    border: 2px solid var(--border-color);
    color: var(--primary-color);
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all var(--transition-normal);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.technologie-detail-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(37, 99, 235, 0.1), transparent);
    transition: left var(--transition-normal);
}

.technologie-detail-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
}

.technologie-detail-btn:hover::before {
    left: 100%;
}

.technologie-detail-btn i {
    font-size: 0.875rem;
}

/* Odkaz na detail technologie */
.technologie-detail-link {
    background: var(--bg-secondary);
    border: 2px solid var(--border-color);
    color: var(--primary-color);
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all var(--transition-normal);
    cursor: pointer;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.technologie-detail-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(37, 99, 235, 0.1), transparent);
    transition: left var(--transition-normal);
}

.technologie-detail-link:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
    text-decoration: none;
}

.technologie-detail-link:hover::before {
    left: 100%;
}

.technologie-detail-link i {
    font-size: 0.875rem;
}

/* Disabled stav pro tlačítko */
.technologie-detail-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* Preview výhod v kartě */
.technologie-advantages-preview {
    margin: 0.75rem 0;
    padding: 0;
    background: transparent;
    border-radius: 0;
    border: none;
}

.technologie-advantages-preview .advantages-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 0.5rem 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.technologie-advantages-preview .advantages-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.technologie-advantages-preview .advantage-item {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    margin-bottom: 0.4rem;
    font-size: 0.875rem;
    line-height: 1.4;
}

.technologie-advantages-preview .advantage-item:last-child {
    margin-bottom: 0;
}

.technologie-advantages-preview .advantage-item i {
    color: var(--success-color);
    font-size: 0.75rem;
    margin-top: 0.1rem;
    flex-shrink: 0;
}

.technologie-advantages-preview .advantage-item span {
    color: var(--text-secondary);
}

/* Kontaktní sekce */
.technologie-contact {
    background: var(--bg-primary);
    padding: 3rem 0;
    margin-top: 2rem;
    border-top: 1px solid var(--border-color);
    text-align: center;
}

.technologie-contact .container {
    padding-left: 20px;
    padding-right: 20px;
}

.contact-title {
    font-size: 2.25rem;
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    letter-spacing: -0.025em;
    line-height: 1.2;
    text-align: center;
}

.contact-description {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: 2.5rem;
    line-height: 1.7;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.contact-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.contact-buttons .btn {
    border-radius: var(--border-radius);
    padding: 1rem 2rem;
    font-weight: 600;
    font-size: 1rem;
    transition: all var(--transition-normal);
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    min-width: 180px;
    justify-content: center;
}

.contact-buttons .btn-primary {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.contact-buttons .btn-primary:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.contact-buttons .btn-outline-primary {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.contact-buttons .btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Prázdný stav */
.technologie-empty {
    padding: 5rem 0;
    background: var(--bg-secondary);
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    max-width: 500px;
    margin: 0 auto;
}

.empty-icon {
    font-size: 5rem;
    color: var(--text-muted);
    margin-bottom: 2rem;
    opacity: 0.6;
}

.empty-title {
    font-size: 2rem;
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-weight: 700;
}

.empty-description {
    color: var(--text-secondary);
    margin-bottom: 2.5rem;
    line-height: 1.6;
    font-size: 1.1rem;
}

.empty-state .btn {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    border-radius: var(--border-radius);
    padding: 0.875rem 2rem;
    font-weight: 600;
    transition: all var(--transition-normal);
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
}

.empty-state .btn:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Chybový stav */
.technologie-error {
    padding: 5rem 0;
    background: var(--bg-secondary);
}

.error-state {
    text-align: center;
    padding: 4rem 2rem;
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(239, 68, 68, 0.2);
    max-width: 500px;
    margin: 0 auto;
}

.error-icon {
    font-size: 5rem;
    color: var(--danger-color);
    margin-bottom: 2rem;
    opacity: 0.8;
}

.error-title {
    font-size: 2rem;
    color: var(--danger-color);
    margin-bottom: 1rem;
    font-weight: 700;
}

.error-message {
    color: var(--text-secondary);
    margin-bottom: 2.5rem;
    line-height: 1.6;
    font-size: 1.1rem;
}

.error-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.error-actions .btn {
    border-radius: var(--border-radius);
    padding: 0.875rem 2rem;
    font-weight: 600;
    transition: all var(--transition-normal);
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    min-width: 140px;
    justify-content: center;
}

.error-actions .btn-primary {
    background: var(--danger-color);
    border-color: var(--danger-color);
    color: white;
}

.error-actions .btn-primary:hover {
    background: #dc2626;
    border-color: #dc2626;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.error-actions .btn-outline-primary {
    background: transparent;
    border: 2px solid var(--text-secondary);
    color: var(--text-secondary);
}

.error-actions .btn-outline-primary:hover {
    background: var(--text-secondary);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Loading animace */
.technologie-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
    background: var(--bg-secondary);
}

.loading-spinner {
    width: 48px;
    height: 48px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    position: relative;
}

.loading-spinner::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    background: var(--primary-color);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    opacity: 0.3;
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 0.3; transform: translate(-50%, -50%) scale(1); }
    50% { opacity: 0.8; transform: translate(-50%, -50%) scale(1.2); }
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive design */
@media (max-width: 1800px) {
    .technologie-grid .container {
        max-width: 100%;
        padding-left: 20px;
        padding-right: 20px;
    }
}

@media (max-width: 1600px) {
    .technologie-grid .container {
        max-width: 100%;
        padding-left: 20px;
        padding-right: 20px;
    }
}

@media (max-width: 1400px) {
    .technologie-grid .container {
        max-width: 100%;
        padding-left: 20px;
        padding-right: 20px;
    }
}

@media (max-width: 1200px) {
    .technologie-grid .container {
        max-width: 100%;
        padding-left: 20px;
        padding-right: 20px;
    }
}

@media (max-width: 992px) {
    .technologie-intro .section-title {
        font-size: 2.5rem;
    }

    .technologie-intro .section-description {
        font-size: 1.125rem;
    }

    .technologie-image {
        height: 240px;
    }

    .contact-title {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .technologie-intro {
        padding: 2rem 0;
    }

    .technologie-intro .container {
        padding-left: 20px;
        padding-right: 20px;
    }

    .technologie-intro .section-title {
        font-size: 2rem;
    }

    .technologie-intro .section-description {
        font-size: 1rem;
    }

    .technologie-image {
        height: 220px;
    }

    .technologie-content {
        padding: 1.5rem;
    }

    .contact-title {
        font-size: 1.75rem;
    }

    .contact-description {
        font-size: 1rem;
    }

    .contact-buttons {
        flex-direction: column;
        align-items: center;
    }

    .contact-buttons .btn {
        width: 100%;
        max-width: 280px;
    }
}

@media (max-width: 576px) {
    .technologie-intro {
        padding: 2rem 0;
    }

    .technologie-intro .section-title {
        font-size: 1.75rem;
    }

    .technologie-grid {
        padding: 2rem 0;
    }

    .technologie-contact {
        padding: 3rem 0;
    }

    .technologie-content {
        padding: 1.25rem;
    }

    .technologie-title {
        font-size: 1.25rem;
    }

    .technologie-description {
        font-size: 0.9rem;
    }

    .empty-state,
    .error-state {
        padding: 2rem 1rem;
    }

    .empty-title,
    .error-title {
        font-size: 1.5rem;
    }
}

/* Animace pro AOS knihovnu */
[data-aos] {
    pointer-events: none;
}

[data-aos].aos-animate {
    pointer-events: auto;
}

/* Accessibility vylepšení - zmírněné focus stavy */
.technologie-card:focus {
    outline: 1px solid var(--primary-color);
    outline-offset: 1px;
    box-shadow: var(--shadow-md);
}

.technologie-detail-btn:focus,
.technologie-detail-link:focus,
.contact-buttons .btn:focus,
.empty-state .btn:focus,
.error-actions .btn:focus {
    outline: 1px solid var(--primary-color);
    outline-offset: 1px;
}

/* Odstranění divného zvýraznění po kliknutí */
.technologie-card:active,
.technologie-card:focus-visible {
    outline: none;
    box-shadow: var(--shadow-md);
}

.technologie-detail-btn:active,
.technologie-detail-link:active {
    outline: none;
}

/* Vylepšené animace - vypnuto */
.technologie-card {
    /* animation: fadeInUp 0.6s ease-out; */
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hover efekty pro lepší UX */
.technologie-card:hover .technologie-title {
    color: var(--primary-color);
    transition: color var(--transition-normal);
}



/* Prefers reduced motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .technologie-card {
        animation: none;
    }

    .technologie-card:hover {
        transform: none;
    }

    .technologie-img {
        transition: none;
    }

    .loading-spinner {
        animation: none;
    }
}

/* Dark mode support (pro budoucí použití) */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #f1f5f9;
        --text-secondary: #cbd5e1;
        --text-muted: #94a3b8;

        --bg-primary: #1e293b;
        --bg-secondary: #0f172a;
        --bg-tertiary: #334155;

        --border-color: #334155;
    }
}

/* Print styly */
@media print {
    .technologie-page {
        background: white !important;
    }

    .technologie-intro {
        background: white !important;
        color: black !important;
        border-radius: 0 !important;
    }

    .technologie-card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
        break-inside: avoid;
        background: white !important;
    }

    .technologie-card:hover {
        transform: none !important;
    }

    .technologie-contact {
        background: white !important;
        border-radius: 0 !important;
    }

    .contact-buttons,
    .technologie-actions,
    .technologie-advantages-preview {
        display: none !important;
    }
}

/* Utility classes - animace vypnuty */
.fade-in {
    /* animation: fadeIn 0.5s ease-in; */
    opacity: 1;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-up {
    /* animation: slideUp 0.6s ease-out; */
    opacity: 1;
    transform: translateY(0);
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== DETAIL STRÁNKA TECHNOLOGIE ===== */

/* Základní layout detail stránky */
.technologie-detail-page {
    background: var(--bg-secondary);
    min-height: 100vh;
}

/* Breadcrumb navigace je nyní spravována PrestaShop systémem */

/* Hero sekce */
.technologie-hero {
    background: var(--bg-primary);
    padding: 4rem 0;
    border-bottom: 1px solid var(--border-color);
}

.hero-content {
    padding-right: 2rem;
}

.hero-title {
    font-size: 3rem;
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    line-height: 1.2;
    letter-spacing: -0.025em;
}

.hero-description {
    font-size: 1.25rem;
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 2.5rem;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero-actions .btn {
    border-radius: var(--border-radius);
    padding: 1rem 2rem;
    font-weight: 600;
    font-size: 1rem;
    transition: all var(--transition-normal);
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    min-width: 180px;
    justify-content: center;
}

.hero-actions .btn-primary {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.hero-actions .btn-primary:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.hero-actions .btn-outline-secondary {
    background: transparent;
    border: 2px solid var(--secondary-color);
    color: var(--secondary-color);
}

.hero-actions .btn-outline-secondary:hover {
    background: var(--secondary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.hero-image {
    position: relative;
}

.hero-image img {
    width: 100%;
    height: auto;
    max-height: 500px;
    object-fit: cover;
    border-radius: var(--border-radius-lg);
}

.placeholder-image {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
    background: var(--bg-tertiary);
    border-radius: var(--border-radius-lg);
    color: var(--text-muted);
}

.placeholder-image i {
    margin-bottom: 1rem;
}

/* Sekce s obsahem */
.technologie-description,
.technologie-advantages,
.technologie-applications,
.technologie-gallery,
.technologie-contact {
    padding: 4rem 0;
}

.technologie-description {
    background: var(--bg-primary);
}

.technologie-advantages {
    background: var(--bg-secondary);
}

.technologie-applications {
    background: var(--bg-primary);
}

.technologie-gallery {
    background: var(--bg-secondary);
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 3rem;
    text-align: center;
    letter-spacing: -0.025em;
    line-height: 1.2;
}

/* Detailní popis */
.description-content {
    font-size: 1.125rem;
    line-height: 1.8;
    color: var(--text-secondary);
}

.description-content p {
    margin-bottom: 1.5rem;
}

/* Výhody technologie */
.advantages-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
}

.advantage-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
}

.advantage-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.advantage-icon {
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.advantage-icon i {
    font-size: 1.25rem;
}

.advantage-content p {
    margin: 0;
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-primary);
}

/* Oblasti použití */
.applications-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.application-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
}

.application-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.application-icon {
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.application-icon i {
    font-size: 1rem;
}

.application-content p {
    margin: 0;
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-primary);
}

/* Galerie realizací */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.gallery-item {
    position: relative;
    aspect-ratio: 4/3;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.gallery-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.gallery-link {
    display: block;
    width: 100%;
    height: 100%;
    position: relative;
    text-decoration: none;
}

.gallery-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.gallery-item:hover .gallery-image {
    transform: scale(1.05);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-overlay i {
    color: white;
    font-size: 2rem;
}

/* Kontaktní sekce detail stránky */
.technologie-contact {
    background: var(--bg-primary);
    border-top: 1px solid var(--border-color);
}

.contact-description {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: 2.5rem;
    line-height: 1.7;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.contact-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.contact-actions .btn {
    border-radius: var(--border-radius);
    padding: 1rem 2rem;
    font-weight: 600;
    font-size: 1rem;
    transition: all var(--transition-normal);
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    min-width: 200px;
    justify-content: center;
}

.contact-actions .btn-primary {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.contact-actions .btn-primary:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.contact-actions .btn-outline-primary {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.contact-actions .btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Navigace na další technologie */
.technologie-navigation {
    background: var(--bg-secondary);
    padding: 2rem 0;
    border-top: 1px solid var(--border-color);
}

.navigation-wrapper {
    text-align: center;
}

.navigation-wrapper .btn {
    border-radius: var(--border-radius);
    padding: 0.875rem 2rem;
    font-weight: 600;
    transition: all var(--transition-normal);
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    background: var(--bg-primary);
    border: 2px solid var(--border-color);
    color: var(--text-primary);
}

.navigation-wrapper .btn:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Responsive design pro detail stránku */
@media (max-width: 992px) {
    .hero-content {
        padding-right: 0;
        margin-bottom: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-description {
        font-size: 1.125rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .gallery-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .technologie-hero {
        padding: 3rem 0;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .hero-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .hero-actions .btn {
        width: 100%;
        max-width: 300px;
        margin: 0 auto;
    }

    .technologie-description,
    .technologie-advantages,
    .technologie-applications,
    .technologie-gallery,
    .technologie-contact {
        padding: 3rem 0;
    }

    .section-title {
        font-size: 1.75rem;
        margin-bottom: 2rem;
    }

    .advantages-grid,
    .applications-grid {
        gap: 1rem;
    }

    .advantage-item,
    .application-item {
        padding: 1rem;
    }

    .gallery-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .contact-actions {
        flex-direction: column;
        align-items: center;
    }

    .contact-actions .btn {
        width: 100%;
        max-width: 280px;
    }
}

@media (max-width: 576px) {
    .technologie-hero {
        padding: 2rem 0;
    }

    .hero-title {
        font-size: 1.75rem;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .description-content {
        font-size: 1rem;
    }

    .advantage-item,
    .application-item {
        padding: 0.875rem;
        gap: 0.75rem;
    }

    .advantage-content p,
    .application-content p {
        font-size: 0.9rem;
    }
}
