{**
 * Chybová šablona pro technologie
 * Kompatibilní s PrestaShop 8.2.0
 *}

{extends file='page.tpl'}

{block name='page_title'}
    <h1 class="page-title">{l s='Chyba při načítání technologií' mod='technologie'}</h1>
{/block}

{block name='page_content'}
<div class="technologie-error">
    <div class="container">
        <div class="row">
            <div class="col-lg-6 mx-auto text-center">
                <div class="error-state">
                    <i class="fas fa-exclamation-triangle error-icon"></i>
                    <h2 class="error-title">{l s='Omlouváme se' mod='technologie'}</h2>
                    <p class="error-message">
                        {if isset($error_message)}
                            {$error_message|escape:'html':'UTF-8'}
                        {else}
                            {l s='<PERSON><PERSON><PERSON> k <PERSON> chyb<PERSON> při načítání technologií potisku.' mod='technologie'}
                        {/if}
                    </p>

                    {* Pokud je to 404 chyba, zobrazíme nápovědu *}
                    {if isset($error_message) && ($error_message|strpos:'nenalezena' !== false || $error_message|strpos:'nebyla nalezena' !== false)}
                        <div class="error-help">
                            <h3>{l s='Možná hledáte:' mod='technologie'}</h3>
                            <p>{l s='Zkuste použít správnou URL strukturu:' mod='technologie'}</p>
                            <ul class="error-suggestions">
                                <li><strong>{l s='Seznam technologií:' mod='technologie'}</strong> <code>/reklamni-potisk</code></li>
                                <li><strong>{l s='Detail technologie:' mod='technologie'}</strong> <code>/reklamni-potisk/sitotisk</code></li>
                            </ul>
                            <p class="error-note">
                                <i class="fas fa-info-circle"></i>
                                {l s='Místo starého formátu URL používejte nový formát uvedený výše.' mod='technologie'}
                            </p>
                        </div>
                    {/if}

                    <div class="error-actions">
                        <a href="/reklamni-potisk" class="btn btn-primary">
                            <i class="fas fa-list"></i>
                            {l s='Zobrazit technologie' mod='technologie'}
                        </a>
                        <button type="button" class="btn btn-secondary" onclick="location.reload()">
                            <i class="fas fa-redo"></i>
                            {l s='Zkusit znovu' mod='technologie'}
                        </button>
                        <a href="{$urls.pages.index}" class="btn btn-outline-secondary">
                            <i class="fas fa-home"></i>
                            {l s='Zpět na hlavní stránku' mod='technologie'}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.technologie-error {
    padding: 40px 0;
}

.error-state {
    padding: 30px;
    background: #f8f9fa;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.error-icon {
    font-size: 4rem;
    color: #ffc107;
    margin-bottom: 20px;
}

.error-title {
    color: #333;
    margin-bottom: 20px;
}

.error-message {
    color: #666;
    font-size: 1.1rem;
    margin-bottom: 25px;
}

.error-help {
    background: #e3f2fd;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
    border-left: 4px solid #2196f3;
}

.error-help h3 {
    color: #1976d2;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.error-suggestions {
    list-style: none;
    padding: 0;
    margin: 15px 0;
}

.error-suggestions li {
    margin-bottom: 10px;
    padding: 8px 12px;
    background: #fff;
    border-radius: 5px;
    border: 1px solid #ddd;
}

.error-suggestions code {
    background: #f5f5f5;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    color: #d63384;
}

.error-note {
    margin-top: 15px;
    padding: 10px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 5px;
    color: #856404;
}

.error-note i {
    margin-right: 8px;
    color: #ffc107;
}

.error-actions {
    margin-top: 30px;
}

.error-actions .btn {
    margin: 5px;
    padding: 12px 24px;
    text-decoration: none;
    border-radius: 6px;
    display: inline-block;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #007bff;
    color: white;
    border: 2px solid #007bff;
}

.btn-primary:hover {
    background: #0056b3;
    border-color: #0056b3;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
    border: 2px solid #6c757d;
}

.btn-secondary:hover {
    background: #545b62;
    border-color: #545b62;
    transform: translateY(-1px);
}

.btn-outline-secondary {
    background: transparent;
    color: #6c757d;
    border: 2px solid #6c757d;
}

.btn-outline-secondary:hover {
    background: #6c757d;
    color: white;
    transform: translateY(-1px);
}

.btn i {
    margin-right: 8px;
}
</style>
{/block}
